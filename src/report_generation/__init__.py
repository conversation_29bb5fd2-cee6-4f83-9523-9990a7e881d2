"""
Report Generation Package
-----------------------
Comprehensive package for generating test reports.
"""

from .report_generator import TestReportGenerator
from .data_collector import TestDataCollector
from .image_handler import <PERSON>Handler
from .base_pdf import DynamicPDF
import os

# Set FPDF font path using the centralized function
from fpdf import FPDF
from .setup_fonts import get_fonts_dir
FPDF.FONT_PATH = get_fonts_dir()

__all__ = [
    'TestReportGenerator',
    'TestDataCollector',
    'ImageHandler',
    'DynamicPDF'
]

__version__ = '1.0.0'