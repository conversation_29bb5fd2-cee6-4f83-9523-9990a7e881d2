"""
Base PDF Module
-------------
Defines the base PDF class with page numbering and basic settings.
"""
import os
from typing import List, Optional, Dict
from fpdf import FPDF
from .layout_config import LAYOUT
# Import the fonts directory function from setup_fonts
from .setup_fonts import get_fonts_dir

class DynamicPDF(FPDF):
    """Enhanced PDF class with standardized table generation capabilities."""

    def __init__(self):
        super().__init__()
        # Set UTF-8 encoding
        self.set_auto_page_break(auto=True, margin=15)

        # Get fonts directory using the simplified approach
        fonts_dir = get_fonts_dir()

        # Add Unicode font support with full paths
        dejavu_regular = os.path.join(fonts_dir, 'DejaVuSansCondensed.ttf')
        dejavu_bold = os.path.join(fonts_dir, 'DejaVuSansCondensed-Bold.ttf')

        # Verify font files exist
        if not os.path.exists(dejavu_regular):
            raise FileNotFoundError(f"DejaVu regular font not found at: {dejavu_regular}")
        if not os.path.exists(dejavu_bold):
            raise FileNotFoundError(f"DejaVu bold font not found at: {dejavu_bold}")

        # Add fonts with full paths
        self.add_font('DejaVu', '', dejavu_regular, uni=True)
        self.add_font('DejaVu', 'B', dejavu_bold, uni=True)
        
        self.layout = LAYOUT
        self._current_headers = []
        self._current_widths = []

        # Standard table styling
        self.table_styles = {
            'header': {
                'font': ('Arial', 'B', 12),
                'fill_color': (230, 230, 230),  # Light gray
                'text_color': (0, 0, 0),  # Black
                'line_color': (128, 128, 128),  # Gray
                'padding': 2,
                'line_spacing': 1.5
            },
            'cell': {
                'font': ('Arial', '', 12),
                'fill_color': (255, 255, 255),  # White
                'text_color': (0, 0, 0),  # Black
                'line_color': (128, 128, 128),  # Gray
                'padding': 2,
                'line_spacing': 1.5
            }
        }

    def create_specification_table(self, specifications: Dict[str, str], title: Optional[str] = None, table_type: str = 'MainTable') -> None:
        """Create a specification-style table with left column headers."""
        if title:
            if table_type == 'MainTable':
                self.add_table_title(title)
            elif table_type == 'SubTable':
                self.add_subsection_table_title(title)

        # Calculate column widths
        label_width = self.w * 0.4
        value_width = self.w - self.l_margin - self.r_margin - label_width

        for label, value in specifications.items():
            label = label.replace('_', ' ')

            # Calculate wrapped text
            label_lines = self._wrap_text(label, label_width - (2 * self.table_styles['header']['padding']))
            value_lines = self._wrap_text(str(value), value_width - (2 * self.table_styles['cell']['padding']))

            # Calculate row height with increased spacing
            line_height = self.font_size

            # Calculate total content height for both cells
            label_height = len(label_lines) * line_height
            value_height = len(value_lines) * line_height

            # Use maximum height plus padding for row
            content_height = max(label_height, value_height)
            row_height = content_height + (2 * self.table_styles['header']['padding'])

            # Check for page break
            if self.get_y() + row_height > self.page_break_trigger:
                self.add_page()

            start_x = self.get_x()
            start_y = self.get_y()

            # Draw label cell
            self.set_fill_color(*self.table_styles['header']['fill_color'])
            self.rect(start_x, start_y, label_width, row_height, 'F')
            self.rect(start_x, start_y, label_width, row_height)

            # Calculate vertical position for perfect centering
            label_y = start_y + (row_height - label_height) / 2

            # Draw label text
            self.set_font(*self.table_styles['header']['font'])
            for line in label_lines:
                self.set_xy(start_x + self.table_styles['header']['padding'], label_y)
                self.cell(label_width - (2 * self.table_styles['header']['padding']),
                          line_height, line, 0, 0, 'L')
                label_y += line_height

            # Draw value cell
            value_x = start_x + label_width
            self.rect(value_x, start_y, value_width, row_height)

            # Calculate vertical position for perfect centering
            value_y = start_y + (row_height - value_height) / 2

            # Draw value text
            self.set_font(*self.table_styles['cell']['font'])
            for line in value_lines:
                self.set_xy(value_x + self.table_styles['cell']['padding'], value_y)
                self.cell(value_width - (2 * self.table_styles['cell']['padding']),
                          line_height, line, 0, 0, 'L')
                value_y += line_height

            self.set_y(start_y + row_height)

    def create_table(self, table_def: dict) -> None:
        """Create a data table with proper line spacing."""

        if 'title' in table_def and table_def['title']:
            self.add_table_title(table_def['title'])

        self._current_headers = table_def['headers']
        self._current_widths = table_def['widths']

        # Calculate line height and spacing
        base_line_height = self.font_size * 0.8
        line_spacing = self.table_styles['header']['line_spacing']

        # Add headers
        header_height = self._add_table_row(
            table_def['headers'],
            table_def['widths'],
            is_header=True,
            line_height=base_line_height,
            line_spacing=line_spacing
        )

        # Add data rows
        for row in table_def['data']:
            self._add_table_row(
                row,
                table_def['widths'],
                is_header=False,
                line_height=base_line_height,
                line_spacing=line_spacing
            )

    def _add_table_headers(self, headers: List[str], widths: List[float]) -> None:
        """Add standardized table headers."""
        style = self.table_styles['header']

        # Apply header styling
        self.set_font(*style['font'])
        self.set_fill_color(*style['fill_color'])
        self.set_text_color(*style['text_color'])
        self.set_draw_color(*style['line_color'])

        # Calculate row height
        row_height = self._calculate_row_height(headers, widths, style['padding'])

        # Check for page break
        if self.get_y() + row_height > self.page_break_trigger:
            self.add_page()

        # Draw headers
        x = self.get_x()
        y = self.get_y()

        for header, width in zip(headers, widths):
            # Draw cell background and border
            self.rect(x, y, width, row_height, 'F')
            self.rect(x, y, width, row_height)

            # Calculate text positioning for center alignment
            lines = self._wrap_text(header, width - (2 * style['padding']))
            line_height = self.font_size * 0.5
            text_height = len(lines) * line_height
            y_text = y + (row_height - text_height) / 2

            # Draw text
            for line in lines:
                text_width = self.get_string_width(line)
                x_text = x + (width - text_width) / 2
                self.set_xy(x_text, y_text)
                self.cell(text_width, line_height, line, 0, 0, '')
                y_text += line_height

            x += width

        self.set_y(y + row_height)

    def _add_table_row(self, row_data: List[str], widths: List[float],
                       is_header: bool = False, line_height: float = None,
                       line_spacing: float = None) -> float:
        """Add a table row with proper line spacing."""
        style = self.table_styles['header'] if is_header else self.table_styles['cell']

        # Calculate wrapped text for each cell
        cell_lines = [
            self._wrap_text(str(cell), width - (2 * style['padding']))
            for cell, width in zip(row_data, widths)
        ]

        # Calculate row height
        max_lines = max(len(lines) for lines in cell_lines)
        content_height = max_lines * line_height * line_spacing
        row_height = content_height + (2 * style['padding'])

        # Check for page break
        if self.get_y() + row_height > self.page_break_trigger:
            self.add_page()
            if not is_header and self._current_headers:
                self._add_table_row(
                    self._current_headers,
                    self._current_widths,
                    is_header=True,
                    line_height=line_height,
                    line_spacing=line_spacing
                )

        start_x = self.get_x()
        start_y = self.get_y()

        # Draw each cell
        x = start_x
        for cell_lines, width in zip(cell_lines, widths):
            # Draw background for headers
            if is_header:
                self.set_fill_color(*style['fill_color'])
                self.rect(x, start_y, width, row_height, 'F')

            self.rect(x, start_y, width, row_height)

            # Calculate vertical position for perfect centering
            content_height = len(cell_lines) * line_height * line_spacing
            y = start_y + (row_height - content_height) / 2

            # Draw text
            self.set_font(*style['font'])
            for line in cell_lines:
                text_width = self.get_string_width(line)
                text_x = x + (width - text_width) / 2  # Center text horizontally
                self.set_xy(text_x, y)
                self.cell(text_width, line_height, line, 0, 0, '')
                y += line_height * line_spacing

            x += width

        self.set_y(start_y + row_height)
        return row_height

    def _calculate_row_height(self, data: List[str], widths: List[float],
                              padding: float) -> float:
        """Calculate required row height based on content."""
        max_height = 0
        for content, width in zip(data, widths):
            lines = self._wrap_text(str(content), width - (2 * padding))
            text_height = len(lines) * self.font_size * 0.5
            max_height = max(max_height, text_height)

        return max_height + (2 * padding)

    def _wrap_text(self, text: str, max_width: float) -> List[str]:
        """Enhanced word-wrap text to fit within specified width."""
        words = str(text).split()
        if not words:
            return ['']

        lines = []
        current_line = []
        current_width = 0
        space_width = self.get_string_width(' ')

        for word in words:
            word_width = self.get_string_width(word)

            # Check if adding this word would exceed the max width
            if current_line and current_width + word_width + space_width > max_width:
                # Add current line to lines
                lines.append(' '.join(current_line))
                current_line = [word]
                current_width = word_width
            else:
                # Add word to current line
                current_line.append(word)
                current_width += word_width + (space_width if current_line else 0)

            # Handle long words that exceed max width
            if word_width > max_width:
                if current_line:
                    lines.append(' '.join(current_line[:-1]))
                # Split long word
                current_word = ''
                for char in word:
                    char_width = self.get_string_width(char)
                    if self.get_string_width(current_word + char) <= max_width:
                        current_word += char
                    else:
                        lines.append(current_word)
                        current_word = char
                if current_word:
                    current_line = [current_word]
                    current_width = self.get_string_width(current_word)

        # Add any remaining text
        if current_line:
            lines.append(' '.join(current_line))

        return lines if lines else ['']

    def _break_long_word(self, word: str, max_width: float) -> List[str]:
        """Break a long word into parts that fit within max_width."""
        parts = []
        current_part = ''
        for char in word:
            if self.get_string_width(current_part + char) <= max_width:
                current_part += char
            else:
                if current_part:
                    parts.append(current_part)
                current_part = char
        if current_part:
            parts.append(current_part)
        return parts

    def add_table_title(self, title: str) -> None:
        """Add standardized table title."""
        self.set_font(*self.layout['fonts']['subsection'])
        self.ln(10)
        self.cell(0, self.layout['cell_heights']['normal'], title, 0, 1, 'L')
        self.ln(3)

    def add_subsection_table_title(self, title: str) -> None:
        """Add standardized table title."""
        self.set_font(*self.layout['fonts']['normal'])
        self.ln(10)
        self.cell(0, self.layout['cell_heights']['normal'], title, 0, 1, 'L')
        self.ln(3)


