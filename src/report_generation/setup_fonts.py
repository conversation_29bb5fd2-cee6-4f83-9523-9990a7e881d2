import os
import sys

# Global variable to store the fonts directory path
FONTS_DIR = None

def get_fonts_dir():
    """Get the fonts directory path - simple and straightforward approach."""
    global FONTS_DIR

    if FONTS_DIR is not None:
        return FONTS_DIR

    try:
        # For PyInstaller builds, use _MEIPASS
        if hasattr(sys, '_MEIPASS'):
            base_path = sys._MEIPASS
        else:
            # For development, get the project root (where gui_main.py is located)
            # This file is in src/report_generation/, so go up 2 levels to reach project root
            if '__file__' in globals():
                base_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            else:
                # Fallback when __file__ is not available (e.g., when running via exec)
                base_path = os.getcwd()

        fonts_dir = os.path.join(base_path, 'fonts')

        # Verify the fonts directory exists and contains required fonts
        required_fonts = ['DejaVuSansCondensed.ttf', 'DejaVuSansCondensed-Bold.ttf']

        if os.path.exists(fonts_dir):
            all_fonts_present = all(
                os.path.exists(os.path.join(fonts_dir, font))
                for font in required_fonts
            )

            if all_fonts_present:
                FONTS_DIR = fonts_dir
                return fonts_dir

        # Fallback: if not found, still return the expected path for error reporting
        FONTS_DIR = fonts_dir
        return fonts_dir

    except Exception as e:
        print(f"Error determining fonts directory: {str(e)}")
        # Last resort fallback
        fallback_path = os.path.join(os.getcwd(), 'fonts')
        FONTS_DIR = fallback_path
        return fallback_path

def setup_fonts():
    """Setup fonts by verifying they exist in the fonts directory."""
    try:
        fonts_dir = get_fonts_dir()
        required_fonts = [
            'DejaVuSansCondensed.ttf',
            'DejaVuSansCondensed-Bold.ttf'
        ]

        print(f"Checking for fonts in: {fonts_dir}")

        if not os.path.exists(fonts_dir):
            print(f"ERROR: Fonts directory not found: {fonts_dir}")
            return False

        # Check if all required fonts exist
        missing_fonts = []
        for font in required_fonts:
            font_path = os.path.join(fonts_dir, font)
            if not os.path.exists(font_path):
                missing_fonts.append(font)

        if missing_fonts:
            print(f"ERROR: Missing required fonts: {missing_fonts}")
            print(f"Expected location: {fonts_dir}")
            return False

        print(f"SUCCESS: All required fonts found in: {fonts_dir}")
        return True

    except Exception as e:
        print(f"Error checking fonts: {str(e)}")
        return False


if __name__ == "__main__":
    setup_fonts()