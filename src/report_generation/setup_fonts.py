import os
import sys

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

    return os.path.join(base_path, relative_path)

def setup_fonts():
    """Setup fonts by verifying they exist in the fonts directory."""
    try:
        # Simply check if fonts exist in the fonts directory
        fonts_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'fonts')
        required_fonts = [
            'DejaVuSansCondensed.ttf',
            'DejaVuSansCondensed-Bold.ttf'
        ]

        # Check if all required fonts exist
        all_fonts_present = all(
            os.path.exists(os.path.join(fonts_dir, font))
            for font in required_fonts
        )

        if all_fonts_present:
            print("Required fonts found")
            return True
        else:
            print(f"Missing required fonts in {fonts_dir}")
            return False

    except Exception as e:
        print(f"Error checking fonts: {str(e)}")
        return False


if __name__ == "__main__":
    setup_fonts()